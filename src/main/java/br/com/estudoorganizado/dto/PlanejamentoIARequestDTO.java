package br.com.estudoorganizado.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

@Data
public class PlanejamentoIARequestDTO {
    
    @NotBlank(message = "A descrição do objetivo de estudo é obrigatória")
    private String objetivoEstudo;
    
    @Min(value = 1, message = "Horas semanais deve ser no mínimo 1")
    @Max(value = 168, message = "Horas semanais não pode exceder 168 (total de horas na semana)")
    private Integer horasDisponiveisPorSemana;
    
    @Min(value = 15, message = "Duração mínima por sessão deve ser de 15 minutos")
    @Max(value = 480, message = "Duração máxima por sessão não pode exceder 8 horas (480 minutos)")
    private Integer minutosDuracaoMaximaPorSessao;
    
    private String disciplinasDesejadas;
    private String nivelConhecimentoGeral;
    private String tempoDisponivel;
    private String preferenciaEstudo;
    private String observacoesAdicionais;
}
