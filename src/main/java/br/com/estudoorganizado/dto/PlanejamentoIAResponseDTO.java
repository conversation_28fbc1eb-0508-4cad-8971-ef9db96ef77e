package br.com.estudoorganizado.dto;

import lombok.Data;
import java.util.List;

@Data
public class PlanejamentoIAResponseDTO {
    
    private String nomePlanejamento;
    private String descricaoGeral;
    private Integer horasDisponiveisPorSemana;
    private Integer minutosDuracaoMaximaPorSessao;
    private String intervalosRevisao;
    private CicloEstudoIADTO cicloEstudo;
    private List<String> dicasEstudo;
    private String observacoes;
    
    @Data
    public static class CicloEstudoIADTO {
        private String nome;
        private List<DisciplinaIADTO> disciplinas;
    }
    
    @Data
    public static class DisciplinaIADTO {
        private String nome;
        private Integer peso;
        private Integer nivelConhecimento;
        private Integer ordem;
        private String tempoEstudoMeta;
        private String justificativa;
    }
}
