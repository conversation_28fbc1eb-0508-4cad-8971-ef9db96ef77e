package br.com.estudoorganizado.dto.gemini;

import lombok.Data;
import java.util.List;

@Data
public class GeminiRequestDTO {
    
    private List<Content> contents;
    private GenerationConfig generationConfig;
    
    @Data
    public static class Content {
        private List<Part> parts;
        
        public Content(String text) {
            this.parts = List.of(new Part(text));
        }
    }
    
    @Data
    public static class Part {
        private String text;
        
        public Part(String text) {
            this.text = text;
        }
    }
    
    @Data
    public static class GenerationConfig {
        private Integer maxOutputTokens;
        private Double temperature;
        private Double topP;
        private Integer topK;
        
        public GenerationConfig() {
            this.maxOutputTokens = 2048;
            this.temperature = 0.7;
            this.topP = 0.8;
            this.topK = 40;
        }
    }
}
