package br.com.estudoorganizado.dto.gemini;

import lombok.Data;
import java.util.List;

@Data
public class GeminiResponseDTO {
    
    private List<Candidate> candidates;
    private UsageMetadata usageMetadata;
    
    @Data
    public static class Candidate {
        private Content content;
        private String finishReason;
        private Integer index;
        private List<SafetyRating> safetyRatings;
    }
    
    @Data
    public static class Content {
        private List<Part> parts;
        private String role;
    }
    
    @Data
    public static class Part {
        private String text;
    }
    
    @Data
    public static class SafetyRating {
        private String category;
        private String probability;
    }
    
    @Data
    public static class UsageMetadata {
        private Integer promptTokenCount;
        private Integer candidatesTokenCount;
        private Integer totalTokenCount;
    }
    
    public String getGeneratedText() {
        if (candidates != null && !candidates.isEmpty() && 
            candidates.get(0).getContent() != null && 
            candidates.get(0).getContent().getParts() != null && 
            !candidates.get(0).getContent().getParts().isEmpty()) {
            return candidates.get(0).getContent().getParts().get(0).getText();
        }
        return null;
    }
}
