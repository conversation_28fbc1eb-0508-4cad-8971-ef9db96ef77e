package br.com.estudoorganizado.service;

import br.com.estudoorganizado.dto.AgendaDTO;
import br.com.estudoorganizado.dto.CreateAgendaDTO;
import br.com.estudoorganizado.model.Agenda;
import br.com.estudoorganizado.model.Disciplina;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.repository.AgendaRepository;
import br.com.estudoorganizado.repository.DisciplinaRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AgendaService {

    private final AgendaRepository agendaRepository;
    private final DisciplinaRepository disciplinaRepository;

    public AgendaDTO create(CreateAgendaDTO dto, User user) {
        Disciplina disciplina = disciplinaRepository.findById(dto.getDisciplinaId())
                .orElseThrow(() -> new RuntimeException("Disciplina não encontrada"));

        Agenda agenda = new Agenda();
        agenda.setDataHoraInicio(dto.getDataHoraInicio());
        agenda.setDataHoraFim(dto.getDataHoraFim());
        agenda.setUser(user);
        agenda.setDisciplina(disciplina);

        Agenda savedAgenda = agendaRepository.save(agenda);
        return toDTO(savedAgenda);
    }

    public AgendaDTO update(Long id, CreateAgendaDTO dto, User user) {
        Agenda agenda = agendaRepository.findByIdAndUserId(id, user.getId())
                .orElseThrow(() -> new RuntimeException("Agendamento não encontrado"));

        Disciplina disciplina = disciplinaRepository.findById(dto.getDisciplinaId())
                .orElseThrow(() -> new RuntimeException("Disciplina não encontrada"));

        agenda.setDataHoraInicio(dto.getDataHoraInicio());
        agenda.setDataHoraFim(dto.getDataHoraFim());
        agenda.setDisciplina(disciplina);

        Agenda savedAgenda = agendaRepository.save(agenda);
        return toDTO(savedAgenda);
    }

    public List<AgendaDTO> findToday(User user) {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        LocalDateTime endOfDay = LocalDate.now().atTime(LocalTime.MAX);
        return findByPeriod(user.getId(), startOfDay, endOfDay);
    }

    public List<AgendaDTO> findThisWeek(User user) {
        LocalDate today = LocalDate.now();
        LocalDateTime startOfWeek = today.with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY)).atStartOfDay();
        LocalDateTime endOfWeek = today.with(TemporalAdjusters.nextOrSame(java.time.DayOfWeek.SUNDAY)).atTime(LocalTime.MAX);
        return findByPeriod(user.getId(), startOfWeek, endOfWeek);
    }

    public List<AgendaDTO> findThisMonth(User user) {
        LocalDate today = LocalDate.now();
        LocalDateTime startOfMonth = today.with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay();
        LocalDateTime endOfMonth = today.with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX);
        return findByPeriod(user.getId(), startOfMonth, endOfMonth);
    }

    private List<AgendaDTO> findByPeriod(Long userId, LocalDateTime start, LocalDateTime end) {
        return agendaRepository.findByUserIdAndDataHoraInicioBetween(userId, start, end)
                .stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }

    private AgendaDTO toDTO(Agenda agenda) {
        AgendaDTO dto = new AgendaDTO();
        dto.setId(agenda.getId());
        dto.setDataHoraInicio(agenda.getDataHoraInicio());
        dto.setDataHoraFim(agenda.getDataHoraFim());
        dto.setDisciplinaId(agenda.getDisciplina().getId());
        dto.setDisciplinaName(agenda.getDisciplina().getNome());
        return dto;
    }


    public List<AgendaDTO> findAll(User user) {
        return agendaRepository.findByUserId(user.getId())
                .stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }
}
