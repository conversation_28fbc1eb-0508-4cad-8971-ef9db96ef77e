package br.com.estudoorganizado.service;

import br.com.estudoorganizado.dto.*;
import br.com.estudoorganizado.exception.GeminiServiceException;
import br.com.estudoorganizado.model.User;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class PlanejamentoInteligentService {

    private final GeminiService geminiService;
    private final PlanejamentoService planejamentoService;
    private final DisciplinaService disciplinaService;
    private final Gson gson = new Gson();

    public PlanejamentoDTO criarPlanejamentoComIA(PlanejamentoIARequestDTO request, User user) {
        try {
            log.info("Iniciando criação de planejamento com IA para usuário: {}", user.getEmail());

            // Gerar prompt para a IA
            String prompt = gerarPrompt(request);

            // Obter resposta da IA
            String respostaIA = geminiService.generateContent(prompt);

            // Processar resposta da IA
            PlanejamentoIAResponseDTO respostaProcessada = processarRespostaIA(respostaIA);

            // Converter para PlanejamentoDTO e salvar
            PlanejamentoDTO planejamentoDTO = converterParaPlanejamentoDTO(respostaProcessada, user);

            // Salvar planejamento
            PlanejamentoDTO planejamentoSalvo = planejamentoService.saveOrUpdate(planejamentoDTO, user);

            log.info("Planejamento criado com sucesso via IA para usuário: {}", user.getEmail());
            return planejamentoSalvo;

        } catch (GeminiServiceException e) {
            log.error("Erro na comunicação com a IA: {}", e.getMessage());
            throw e; // Re-throw para manter o tipo específico da exceção
        } catch (Exception e) {
            log.error("Erro ao criar planejamento com IA: {}", e.getMessage());
            throw new RuntimeException("Erro ao processar resposta da IA: " + e.getMessage(), e);
        }
    }

    private String gerarPrompt(PlanejamentoIARequestDTO request) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("Você é um especialista em planejamento de estudos para concursos públicos. ");
        prompt.append("Crie um planejamento de estudos detalhado baseado nas informações fornecidas.\n\n");

        prompt.append("INFORMAÇÕES DO USUÁRIO:\n");
        prompt.append("- Objetivo: ").append(request.getObjetivoEstudo()).append("\n");
        prompt.append("- Horas disponíveis por semana: ").append(request.getHorasDisponiveisPorSemana()).append("\n");
        prompt.append("- Duração máxima por sessão: ").append(request.getMinutosDuracaoMaximaPorSessao()).append(" minutos\n");

        if (request.getDisciplinasDesejadas() != null) {
            prompt.append("- Disciplinas desejadas: ").append(request.getDisciplinasDesejadas()).append("\n");
        }
        if (request.getNivelConhecimentoGeral() != null) {
            prompt.append("- Nível de conhecimento geral: ").append(request.getNivelConhecimentoGeral()).append("\n");
        }
        if (request.getTempoDisponivel() != null) {
            prompt.append("- Tempo disponível: ").append(request.getTempoDisponivel()).append("\n");
        }
        if (request.getPreferenciaEstudo() != null) {
            prompt.append("- Preferências de estudo: ").append(request.getPreferenciaEstudo()).append("\n");
        }
        if (request.getObservacoesAdicionais() != null) {
            prompt.append("- Observações adicionais: ").append(request.getObservacoesAdicionais()).append("\n");
        }

        prompt.append("\nRETORNE APENAS UM JSON válido com a seguinte estrutura:\n");
        prompt.append("{\n");
        prompt.append("  \"nomePlanejamento\": \"Nome do planejamento\",\n");
        prompt.append("  \"descricaoGeral\": \"Descrição geral do planejamento\",\n");
        prompt.append("  \"horasDisponiveisPorSemana\": ").append(request.getHorasDisponiveisPorSemana()).append(",\n");
        prompt.append("  \"minutosDuracaoMaximaPorSessao\": ").append(request.getMinutosDuracaoMaximaPorSessao()).append(",\n");
        prompt.append("  \"intervalosRevisao\": \"1,3,7,14,30,60,120\",\n");
        prompt.append("  \"cicloEstudo\": {\n");
        prompt.append("    \"nome\": \"Nome do ciclo\",\n");
        prompt.append("    \"disciplinas\": [\n");
        prompt.append("      {\n");
        prompt.append("        \"nome\": \"Nome da disciplina\",\n");
        prompt.append("        \"peso\": 5,\n");
        prompt.append("        \"nivelConhecimento\": 3,\n");
        prompt.append("        \"ordem\": 1,\n");
        prompt.append("        \"tempoEstudoMeta\": \"02:30:00\",\n");
        prompt.append("        \"justificativa\": \"Justificativa para o peso e tempo\"\n");
        prompt.append("      }\n");
        prompt.append("    ]\n");
        prompt.append("  },\n");
        prompt.append("  \"dicasEstudo\": [\"Dica 1\", \"Dica 2\"],\n");
        prompt.append("  \"observacoes\": \"Observações importantes\"\n");
        prompt.append("}\n\n");

        prompt.append("REGRAS IMPORTANTES:\n");
        prompt.append("- Peso: 1-5 (1=baixa importância, 5=alta importância)\n");
        prompt.append("- Nível de conhecimento: 1-5 (1=iniciante, 5=avançado)\n");
        prompt.append("- Tempo de estudo meta no formato HH:MM:SS\n");
        prompt.append("- Distribua o tempo total disponível entre as disciplinas\n");
        prompt.append("- Considere o nível de conhecimento para definir prioridades\n");
        prompt.append("- Retorne APENAS o JSON, sem texto adicional\n");

        return prompt.toString();
    }

    private PlanejamentoIAResponseDTO processarRespostaIA(String respostaIA) {
        try {
            // Limpar a resposta removendo possíveis caracteres extras
            String jsonLimpo = limparJSON(respostaIA);

            log.debug("JSON limpo para parsing: {}", jsonLimpo);

            // Converter JSON para DTO
            PlanejamentoIAResponseDTO resposta = gson.fromJson(jsonLimpo, PlanejamentoIAResponseDTO.class);

            // Validar resposta
            validarRespostaIA(resposta);

            return resposta;

        } catch (JsonSyntaxException e) {
            log.error("Erro ao fazer parse do JSON da IA: {}", e.getMessage());
            log.error("JSON recebido: {}", respostaIA);
            throw new RuntimeException("Resposta da IA não está em formato JSON válido", e);
        }
    }

    private String limparJSON(String json) {
        // Remove possíveis caracteres de markdown ou texto extra
        json = json.trim();

        // Remove ```json e ``` se existirem
        if (json.startsWith("```json")) {
            json = json.substring(7);
        }
        if (json.startsWith("```")) {
            json = json.substring(3);
        }
        if (json.endsWith("```")) {
            json = json.substring(0, json.length() - 3);
        }

        // Encontra o primeiro { e o último }
        int inicio = json.indexOf('{');
        int fim = json.lastIndexOf('}');

        if (inicio != -1 && fim != -1 && fim > inicio) {
            json = json.substring(inicio, fim + 1);
        }

        return json.trim();
    }

    private void validarRespostaIA(PlanejamentoIAResponseDTO resposta) {
        if (resposta == null) {
            throw new RuntimeException("Resposta da IA é nula");
        }

        if (resposta.getNomePlanejamento() == null || resposta.getNomePlanejamento().trim().isEmpty()) {
            throw new RuntimeException("Nome do planejamento não foi fornecido pela IA");
        }

        if (resposta.getCicloEstudo() == null || resposta.getCicloEstudo().getDisciplinas() == null ||
            resposta.getCicloEstudo().getDisciplinas().isEmpty()) {
            throw new RuntimeException("Disciplinas não foram fornecidas pela IA");
        }

        // Validar cada disciplina
        for (PlanejamentoIAResponseDTO.DisciplinaIADTO disciplina : resposta.getCicloEstudo().getDisciplinas()) {
            if (disciplina.getNome() == null || disciplina.getNome().trim().isEmpty()) {
                throw new RuntimeException("Nome da disciplina não foi fornecido pela IA");
            }
            if (disciplina.getPeso() == null || disciplina.getPeso() < 1 || disciplina.getPeso() > 5) {
                disciplina.setPeso(3); // Valor padrão
            }
            if (disciplina.getNivelConhecimento() == null || disciplina.getNivelConhecimento() < 1 || disciplina.getNivelConhecimento() > 5) {
                disciplina.setNivelConhecimento(3); // Valor padrão
            }
        }
    }

    private PlanejamentoDTO converterParaPlanejamentoDTO(PlanejamentoIAResponseDTO respostaIA, User user) {
        PlanejamentoDTO planejamentoDTO = new PlanejamentoDTO();

        planejamentoDTO.setNome(respostaIA.getNomePlanejamento());
        planejamentoDTO.setHorasDisponiveisPorSemana(respostaIA.getHorasDisponiveisPorSemana());
        planejamentoDTO.setMinutosDuracaoMaximaPorSessao(respostaIA.getMinutosDuracaoMaximaPorSessao());
        planejamentoDTO.setIntervalosRevisao(respostaIA.getIntervalosRevisao());

        // Converter ciclo de estudo
        if (respostaIA.getCicloEstudo() != null) {
            CicloEstudoDTO cicloEstudoDTO = new CicloEstudoDTO();
            cicloEstudoDTO.setNome(respostaIA.getCicloEstudo().getNome());

            // Converter disciplinas
            List<CicloEstudoDisciplinaDTO> disciplinasDTO = new ArrayList<>();

            for (PlanejamentoIAResponseDTO.DisciplinaIADTO disciplinaIA : respostaIA.getCicloEstudo().getDisciplinas()) {
                CicloEstudoDisciplinaDTO disciplinaDTO = new CicloEstudoDisciplinaDTO();

                // Criar ou buscar disciplina
                DisciplinaDTO disciplina = criarOuBuscarDisciplina(disciplinaIA.getNome(), user);
                disciplinaDTO.setDisciplina(disciplina);

                disciplinaDTO.setPeso(disciplinaIA.getPeso());
                disciplinaDTO.setNivelConhecimento(disciplinaIA.getNivelConhecimento());
                disciplinaDTO.setOrdem(disciplinaIA.getOrdem());

                // Converter tempo de estudo meta
                if (disciplinaIA.getTempoEstudoMeta() != null) {
                    try {
                        disciplinaDTO.setTempoEstudoMeta(LocalTime.parse(disciplinaIA.getTempoEstudoMeta()));
                    } catch (Exception e) {
                        log.warn("Erro ao converter tempo de estudo meta: {}", disciplinaIA.getTempoEstudoMeta());
                        disciplinaDTO.setTempoEstudoMeta(LocalTime.of(1, 0)); // 1 hora padrão
                    }
                }

                disciplinasDTO.add(disciplinaDTO);
            }

            cicloEstudoDTO.setDisciplinas(disciplinasDTO);
            planejamentoDTO.setCicloEstudo(cicloEstudoDTO);
        }

        return planejamentoDTO;
    }

    private DisciplinaDTO criarOuBuscarDisciplina(String nomeDisciplina, User user) {
        // Buscar disciplinas existentes do usuário
        List<DisciplinaDTO> disciplinasExistentes = disciplinaService.findAllByUserId(user.getId());

        // Verificar se já existe uma disciplina com nome similar
        for (DisciplinaDTO disciplina : disciplinasExistentes) {
            if (disciplina.getNome().equalsIgnoreCase(nomeDisciplina.trim())) {
                return disciplina;
            }
        }

        // Se não existe, criar nova disciplina
        DisciplinaDTO novaDisciplina = new DisciplinaDTO();
        novaDisciplina.setNome(nomeDisciplina.trim());

        return disciplinaService.save(novaDisciplina, user);
    }
