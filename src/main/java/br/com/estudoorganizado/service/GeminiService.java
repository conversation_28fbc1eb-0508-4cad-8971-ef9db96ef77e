package br.com.estudoorganizado.service;

import br.com.estudoorganizado.config.GeminiConfig;
import br.com.estudoorganizado.dto.gemini.GeminiRequestDTO;
import br.com.estudoorganizado.dto.gemini.GeminiResponseDTO;
import br.com.estudoorganizado.exception.GeminiServiceException;
import com.google.gson.Gson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class GeminiService {
    
    private final GeminiConfig geminiConfig;
    private final Gson gson = new Gson();
    private final OkHttpClient httpClient;
    
    public GeminiService(GeminiConfig geminiConfig) {
        this.geminiConfig = geminiConfig;

        // Validar configuração
        if (geminiConfig.getApiKey() == null || geminiConfig.getApiKey().trim().isEmpty() ||
            geminiConfig.getApiKey().equals("your-gemini-api-key-here")) {
            throw new GeminiServiceException.ConfigurationException("Chave da API Gemini não configurada");
        }

        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(geminiConfig.getTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(geminiConfig.getTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(geminiConfig.getTimeout(), TimeUnit.MILLISECONDS)
                .build();
    }
    
    public String generateContent(String prompt) {
        log.info("Enviando requisição para Gemini API");

        try {
            // Validar entrada
            if (prompt == null || prompt.trim().isEmpty()) {
                throw new GeminiServiceException.InvalidResponseException("Prompt não pode ser vazio");
            }

            // Criar requisição
            GeminiRequestDTO request = createRequest(prompt);
            String jsonRequest = gson.toJson(request);

            log.debug("Request JSON: {}", jsonRequest);

            // Criar corpo da requisição HTTP
            RequestBody body = RequestBody.create(
                jsonRequest,
                MediaType.get("application/json; charset=utf-8")
            );

            // Construir URL com API key
            String url = geminiConfig.getApiUrl() + "?key=" + geminiConfig.getApiKey();

            // Criar requisição HTTP
            Request httpRequest = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // Executar requisição
            try (Response response = httpClient.newCall(httpRequest).execute()) {
                return processResponse(response);
            }

        } catch (IOException e) {
            log.error("Erro de comunicação com Gemini API: {}", e.getMessage());
            throw new GeminiServiceException.CommunicationException(e.getMessage(), e);
        } catch (Exception e) {
            log.error("Erro inesperado ao comunicar com Gemini API: {}", e.getMessage());
            throw new GeminiServiceException("Erro inesperado: " + e.getMessage(), e);
        }
    }

    private String processResponse(Response response) throws IOException {
        if (!response.isSuccessful()) {
            String errorBody = response.body() != null ? response.body().string() : "Unknown error";
            log.error("Erro na requisição para Gemini API. Status: {}, Body: {}", response.code(), errorBody);

            if (response.code() == 401) {
                throw new GeminiServiceException.ConfigurationException("Chave da API inválida ou expirada");
            } else if (response.code() == 429) {
                throw new GeminiServiceException.CommunicationException("Limite de requisições excedido");
            } else if (response.code() >= 500) {
                throw new GeminiServiceException.CommunicationException("Erro interno do servidor Gemini");
            } else {
                throw new GeminiServiceException.CommunicationException("Erro HTTP " + response.code() + ": " + errorBody);
            }
        }

        String responseBody = response.body().string();
        log.debug("Response JSON: {}", responseBody);

        // Parse da resposta
        try {
            GeminiResponseDTO geminiResponse = gson.fromJson(responseBody, GeminiResponseDTO.class);
            String generatedText = geminiResponse.getGeneratedText();

            if (generatedText == null || generatedText.trim().isEmpty()) {
                throw new GeminiServiceException.InvalidResponseException("Resposta vazia da Gemini API");
            }

            log.info("Resposta recebida com sucesso da Gemini API");
            return generatedText;

        } catch (Exception e) {
            log.error("Erro ao processar resposta da Gemini API: {}", e.getMessage());
            throw new GeminiServiceException.InvalidResponseException("Erro ao processar resposta: " + e.getMessage(), e);
        }
    }
    
    private GeminiRequestDTO createRequest(String prompt) {
        GeminiRequestDTO request = new GeminiRequestDTO();
        request.setContents(List.of(new GeminiRequestDTO.Content(prompt)));
        request.setGenerationConfig(new GeminiRequestDTO.GenerationConfig());
        return request;
    }
}
