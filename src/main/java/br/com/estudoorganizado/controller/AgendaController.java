package br.com.estudoorganizado.controller;

import java.util.List;

import br.com.estudoorganizado.model.Agenda;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import br.com.estudoorganizado.dto.AgendaDTO;
import br.com.estudoorganizado.dto.CreateAgendaDTO;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.service.AgendaService;

@RestController
@RequestMapping("/v1/agendas")
public class AgendaController {

    @Autowired
    private AgendaService agendaService;

    @PostMapping
    public ResponseEntity<AgendaDTO> create(@RequestBody CreateAgendaDTO dto, @AuthenticationPrincipal User user) {
        AgendaDTO createdAgenda = agendaService.create(dto, user);
        return ResponseEntity.status(201).body(createdAgenda);
    }

    @PutMapping("{id}")
    public ResponseEntity<AgendaDTO> update(@PathVariable Long id, @RequestBody CreateAgendaDTO dto, @AuthenticationPrincipal User user) {
        AgendaDTO updateAgenda = agendaService.update(id, dto, user);
        return ResponseEntity.ok(updateAgenda);
    }

    @GetMapping
    public ResponseEntity<List<AgendaDTO>> findAll(@AuthenticationPrincipal User user) {
        return ResponseEntity.ok(agendaService.findAll(user));
    }

    @GetMapping("/today")
    public ResponseEntity<List<AgendaDTO>> getTodaySchedule(@AuthenticationPrincipal User user) {
        List<AgendaDTO> schedule = agendaService.findToday(user);
        return ResponseEntity.ok(schedule);
    }

    @GetMapping("/week")
    public ResponseEntity<List<AgendaDTO>> getWeekSchedule(@AuthenticationPrincipal User user) {
        List<AgendaDTO> schedule = agendaService.findThisWeek(user);
        return ResponseEntity.ok(schedule);
    }

    @GetMapping("/month")
    public ResponseEntity<List<AgendaDTO>> getMonthSchedule(@AuthenticationPrincipal User user) {
        List<AgendaDTO> schedule = agendaService.findThisMonth(user);
        return ResponseEntity.ok(schedule);
    }
}