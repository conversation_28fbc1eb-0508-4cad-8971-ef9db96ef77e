package br.com.estudoorganizado.controller;

import br.com.estudoorganizado.dto.RevisaoEspacadaDTO;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.service.RevisaoEspacadaService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/v1/revisoes-espacadas")
@RequiredArgsConstructor
public class RevisaoEspacadaController {

    private final RevisaoEspacadaService revisaoEspacadaService;

    @GetMapping("/pendentes")
    public ResponseEntity<List<RevisaoEspacadaDTO>> buscarRevisoesPendentes(@AuthenticationPrincipal User user) {
        try {
            List<RevisaoEspacadaDTO> revisoes = revisaoEspacadaService.buscarRevisoesPendentes(user.getId());
            return ResponseEntity.ok(revisoes);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/todas-pendentes")
    public ResponseEntity<List<RevisaoEspacadaDTO>> buscarTodasRevisoesPendentes(@AuthenticationPrincipal User user) {
        try {
            List<RevisaoEspacadaDTO> revisoes = revisaoEspacadaService.buscarTodasRevisoesPendentes(user.getId());
            return ResponseEntity.ok(revisoes);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/gerar/{registroEstudoId}")
    public ResponseEntity<Void> gerarRevisoesEspacadas(@PathVariable Long registroEstudoId, @AuthenticationPrincipal User user) {
        try {
            revisaoEspacadaService.gerarRevisoesEspacadas(registroEstudoId, user);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/concluir/{revisaoId}")
    public ResponseEntity<Void> marcarComoConcluida(
            @PathVariable Long revisaoId,
            @RequestParam Integer nivelDificuldade,
            @AuthenticationPrincipal User user) {
        try {
            revisaoEspacadaService.marcarComoConcluida(revisaoId, nivelDificuldade, user.getId());
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/contar-pendentes")
    public ResponseEntity<Long> contarRevisoesPendentes(@AuthenticationPrincipal User user) {
        try {
            Long count = revisaoEspacadaService.contarRevisoesPendentes(user.getId());
            return ResponseEntity.ok(count);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
} 