package br.com.estudoorganizado.controller;

import br.com.estudoorganizado.dto.ConfiguracaoDTO;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.service.ConfiguracaoService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/v1/configuracoes")
@RequiredArgsConstructor
public class ConfiguracaoController {

    private final ConfiguracaoService configuracaoService;

    @GetMapping
    public ResponseEntity<ConfiguracaoDTO> getConfiguration(@AuthenticationPrincipal User user) {
        ConfiguracaoDTO config = configuracaoService.getConfiguration(user.getId());
        return ResponseEntity.ok(config);
    }

    @PutMapping
    public ResponseEntity<ConfiguracaoDTO> updateConfiguration(@AuthenticationPrincipal User user, @Valid @RequestBody ConfiguracaoDTO configuracaoDTO) {
        ConfiguracaoDTO updatedConfig = configuracaoService.updateConfiguration(user.getId(), configuracaoDTO);
        return ResponseEntity.ok(updatedConfig);
    }
} 