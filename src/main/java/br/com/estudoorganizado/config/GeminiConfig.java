package br.com.estudoorganizado.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "gemini.api")
public class GeminiConfig {
    
    private String key;
    private String url;
    private Integer timeout;
    private Integer maxTokens;
    
    public String getApiKey() {
        return key;
    }
    
    public String getApiUrl() {
        return url;
    }
    
    public Integer getTimeout() {
        return timeout != null ? timeout : 30000;
    }
    
    public Integer getMaxTokens() {
        return maxTokens != null ? maxTokens : 2048;
    }
}
