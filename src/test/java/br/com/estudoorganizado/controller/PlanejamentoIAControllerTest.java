package br.com.estudoorganizado.controller;

import br.com.estudoorganizado.dto.PlanejamentoDTO;
import br.com.estudoorganizado.dto.PlanejamentoIARequestDTO;
import br.com.estudoorganizado.exception.GeminiServiceException;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.service.PlanejamentoInteligentService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(PlanejamentoIAController.class)
class PlanejamentoIAControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PlanejamentoInteligentService planejamentoInteligentService;

    @Autowired
    private ObjectMapper objectMapper;

    private User testUser;
    private PlanejamentoIARequestDTO testRequest;
    private PlanejamentoDTO testResponse;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(1L);
        testUser.setEmail("<EMAIL>");
        testUser.setName("Test User");

        testRequest = new PlanejamentoIARequestDTO();
        testRequest.setObjetivoEstudo("Concurso Público Federal");
        testRequest.setHorasDisponiveisPorSemana(20);
        testRequest.setMinutosDuracaoMaximaPorSessao(120);
        testRequest.setDisciplinasDesejadas("Português, Matemática");

        testResponse = new PlanejamentoDTO();
        testResponse.setId(1L);
        testResponse.setNome("Planejamento Gerado");
        testResponse.setHorasDisponiveisPorSemana(20);
        testResponse.setMinutosDuracaoMaximaPorSessao(120);
    }

    @Test
    @WithMockUser
    void testGerarPlanejamentoComIA_Success_ShouldReturn201() throws Exception {
        // Arrange
        when(planejamentoInteligentService.criarPlanejamentoComIA(any(PlanejamentoIARequestDTO.class), any(User.class)))
            .thenReturn(testResponse);

        // Act & Assert
        mockMvc.perform(post("/v1/planejamento-ia/gerar")
                .with(user(testUser))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").value(testResponse.getId()))
                .andExpect(jsonPath("$.nome").value(testResponse.getNome()))
                .andExpect(jsonPath("$.horasDisponiveisPorSemana").value(testResponse.getHorasDisponiveisPorSemana()));

        verify(planejamentoInteligentService).criarPlanejamentoComIA(any(PlanejamentoIARequestDTO.class), any(User.class));
    }

    @Test
    @WithMockUser
    void testGerarPlanejamentoComIA_InvalidRequest_ShouldReturn400() throws Exception {
        // Arrange
        PlanejamentoIARequestDTO invalidRequest = new PlanejamentoIARequestDTO();
        // Deixar campos obrigatórios vazios

        // Act & Assert
        mockMvc.perform(post("/v1/planejamento-ia/gerar")
                .with(user(testUser))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());

        verify(planejamentoInteligentService, never()).criarPlanejamentoComIA(any(), any());
    }

    @Test
    @WithMockUser
    void testGerarPlanejamentoComIA_ConfigurationError_ShouldReturn500() throws Exception {
        // Arrange
        when(planejamentoInteligentService.criarPlanejamentoComIA(any(PlanejamentoIARequestDTO.class), any(User.class)))
            .thenThrow(new GeminiServiceException.ConfigurationException("API key not configured"));

        // Act & Assert
        mockMvc.perform(post("/v1/planejamento-ia/gerar")
                .with(user(testUser))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRequest)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.message").value("Serviço de IA não configurado corretamente. Contate o administrador."));

        verify(planejamentoInteligentService).criarPlanejamentoComIA(any(PlanejamentoIARequestDTO.class), any(User.class));
    }

    @Test
    @WithMockUser
    void testGerarPlanejamentoComIA_CommunicationError_ShouldReturn503() throws Exception {
        // Arrange
        when(planejamentoInteligentService.criarPlanejamentoComIA(any(PlanejamentoIARequestDTO.class), any(User.class)))
            .thenThrow(new GeminiServiceException.CommunicationException("API temporarily unavailable"));

        // Act & Assert
        mockMvc.perform(post("/v1/planejamento-ia/gerar")
                .with(user(testUser))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRequest)))
                .andExpect(status().isServiceUnavailable())
                .andExpect(jsonPath("$.message").value("Serviço de IA temporariamente indisponível. Tente novamente em alguns minutos."));

        verify(planejamentoInteligentService).criarPlanejamentoComIA(any(PlanejamentoIARequestDTO.class), any(User.class));
    }

    @Test
    @WithMockUser
    void testGerarPlanejamentoComIA_InvalidResponse_ShouldReturn500() throws Exception {
        // Arrange
        when(planejamentoInteligentService.criarPlanejamentoComIA(any(PlanejamentoIARequestDTO.class), any(User.class)))
            .thenThrow(new GeminiServiceException.InvalidResponseException("Invalid JSON response"));

        // Act & Assert
        mockMvc.perform(post("/v1/planejamento-ia/gerar")
                .with(user(testUser))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRequest)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.message").value("Erro ao processar resposta da IA. Tente reformular sua solicitação."));

        verify(planejamentoInteligentService).criarPlanejamentoComIA(any(PlanejamentoIARequestDTO.class), any(User.class));
    }

    @Test
    @WithMockUser
    void testObterSugestaoIA_Success_ShouldReturn200() throws Exception {
        // Arrange
        when(planejamentoInteligentService.criarPlanejamentoComIA(any(PlanejamentoIARequestDTO.class), any(User.class)))
            .thenReturn(testResponse);

        // Act & Assert
        mockMvc.perform(post("/v1/planejamento-ia/sugestao")
                .with(user(testUser))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testResponse.getId()))
                .andExpect(jsonPath("$.nome").value(testResponse.getNome()));

        verify(planejamentoInteligentService).criarPlanejamentoComIA(any(PlanejamentoIARequestDTO.class), any(User.class));
    }

    @Test
    void testGerarPlanejamentoComIA_Unauthorized_ShouldReturn401() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/v1/planejamento-ia/gerar")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRequest)))
                .andExpect(status().isUnauthorized());

        verify(planejamentoInteligentService, never()).criarPlanejamentoComIA(any(), any());
    }

    @Test
    @WithMockUser
    void testGerarPlanejamentoComIA_UnexpectedError_ShouldReturn500() throws Exception {
        // Arrange
        when(planejamentoInteligentService.criarPlanejamentoComIA(any(PlanejamentoIARequestDTO.class), any(User.class)))
            .thenThrow(new RuntimeException("Unexpected error"));

        // Act & Assert
        mockMvc.perform(post("/v1/planejamento-ia/gerar")
                .with(user(testUser))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRequest)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.message").value("Erro interno: Unexpected error"));

        verify(planejamentoInteligentService).criarPlanejamentoComIA(any(PlanejamentoIARequestDTO.class), any(User.class));
    }
}
