package br.com.estudoorganizado.service;

import br.com.estudoorganizado.dto.*;
import br.com.estudoorganizado.exception.GeminiServiceException;
import br.com.estudoorganizado.model.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PlanejamentoInteligentServiceTest {

    @Mock
    private GeminiService geminiService;

    @Mock
    private PlanejamentoService planejamentoService;

    @Mock
    private DisciplinaService disciplinaService;

    private PlanejamentoInteligentService planejamentoInteligentService;

    private User testUser;
    private PlanejamentoIARequestDTO testRequest;

    @BeforeEach
    void setUp() {
        planejamentoInteligentService = new PlanejamentoInteligentService(
            geminiService, planejamentoService, disciplinaService);

        testUser = new User();
        testUser.setId(1L);
        testUser.setEmail("<EMAIL>");

        testRequest = new PlanejamentoIARequestDTO();
        testRequest.setObjetivoEstudo("Concurso Público Federal");
        testRequest.setHorasDisponiveisPorSemana(20);
        testRequest.setMinutosDuracaoMaximaPorSessao(120);
        testRequest.setDisciplinasDesejadas("Português, Matemática, Direito");
        testRequest.setNivelConhecimentoGeral("Intermediário");
    }

    @Test
    void testCriarPlanejamentoComIA_Success_ShouldReturnPlanejamento() {
        // Arrange
        String geminiResponse = "{\n" +
            "  \"nomePlanejamento\": \"Planejamento Concurso Federal\",\n" +
            "  \"descricaoGeral\": \"Planejamento focado em concurso público\",\n" +
            "  \"horasDisponiveisPorSemana\": 20,\n" +
            "  \"minutosDuracaoMaximaPorSessao\": 120,\n" +
            "  \"intervalosRevisao\": \"1,3,7,14,30,60,120\",\n" +
            "  \"cicloEstudo\": {\n" +
            "    \"nome\": \"Ciclo Preparatório\",\n" +
            "    \"disciplinas\": [\n" +
            "      {\n" +
            "        \"nome\": \"Português\",\n" +
            "        \"peso\": 5,\n" +
            "        \"nivelConhecimento\": 3,\n" +
            "        \"ordem\": 1,\n" +
            "        \"tempoEstudoMeta\": \"06:00:00\",\n" +
            "        \"justificativa\": \"Disciplina fundamental\"\n" +
            "      },\n" +
            "      {\n" +
            "        \"nome\": \"Matemática\",\n" +
            "        \"peso\": 4,\n" +
            "        \"nivelConhecimento\": 2,\n" +
            "        \"ordem\": 2,\n" +
            "        \"tempoEstudoMeta\": \"08:00:00\",\n" +
            "        \"justificativa\": \"Precisa de mais atenção\"\n" +
            "      }\n" +
            "    ]\n" +
            "  },\n" +
            "  \"dicasEstudo\": [\"Faça resumos\", \"Pratique exercícios\"],\n" +
            "  \"observacoes\": \"Mantenha consistência\"\n" +
            "}";

        DisciplinaDTO disciplinaPortugues = new DisciplinaDTO();
        disciplinaPortugues.setId(1L);
        disciplinaPortugues.setNome("Português");

        DisciplinaDTO disciplinaMatematica = new DisciplinaDTO();
        disciplinaMatematica.setId(2L);
        disciplinaMatematica.setNome("Matemática");

        PlanejamentoDTO expectedPlanejamento = new PlanejamentoDTO();
        expectedPlanejamento.setId(1L);
        expectedPlanejamento.setNome("Planejamento Concurso Federal");

        when(geminiService.generateContent(anyString())).thenReturn(geminiResponse);
        when(disciplinaService.findAllByUserId(testUser.getId())).thenReturn(Arrays.asList());
        when(disciplinaService.save(any(DisciplinaDTO.class), eq(testUser)))
            .thenReturn(disciplinaPortugues)
            .thenReturn(disciplinaMatematica);
        when(planejamentoService.saveOrUpdate(any(PlanejamentoDTO.class), eq(testUser)))
            .thenReturn(expectedPlanejamento);

        // Act
        PlanejamentoDTO result = planejamentoInteligentService.criarPlanejamentoComIA(testRequest, testUser);

        // Assert
        assertNotNull(result);
        assertEquals(expectedPlanejamento.getId(), result.getId());
        assertEquals(expectedPlanejamento.getNome(), result.getNome());

        verify(geminiService).generateContent(anyString());
        verify(planejamentoService).saveOrUpdate(any(PlanejamentoDTO.class), eq(testUser));
    }

    @Test
    void testCriarPlanejamentoComIA_GeminiError_ShouldThrowException() {
        // Arrange
        when(geminiService.generateContent(anyString()))
            .thenThrow(new GeminiServiceException.CommunicationException("API Error"));

        // Act & Assert
        assertThrows(GeminiServiceException.CommunicationException.class, 
            () -> planejamentoInteligentService.criarPlanejamentoComIA(testRequest, testUser));

        verify(geminiService).generateContent(anyString());
        verify(planejamentoService, never()).saveOrUpdate(any(), any());
    }

    @Test
    void testCriarPlanejamentoComIA_InvalidJsonResponse_ShouldThrowException() {
        // Arrange
        String invalidJson = "{ invalid json }";
        when(geminiService.generateContent(anyString())).thenReturn(invalidJson);

        // Act & Assert
        assertThrows(RuntimeException.class, 
            () -> planejamentoInteligentService.criarPlanejamentoComIA(testRequest, testUser));

        verify(geminiService).generateContent(anyString());
        verify(planejamentoService, never()).saveOrUpdate(any(), any());
    }

    @Test
    void testCriarPlanejamentoComIA_EmptyResponse_ShouldThrowException() {
        // Arrange
        String emptyResponse = "{\n" +
            "  \"nomePlanejamento\": \"\",\n" +
            "  \"cicloEstudo\": {\n" +
            "    \"disciplinas\": []\n" +
            "  }\n" +
            "}";
        when(geminiService.generateContent(anyString())).thenReturn(emptyResponse);

        // Act & Assert
        assertThrows(RuntimeException.class, 
            () -> planejamentoInteligentService.criarPlanejamentoComIA(testRequest, testUser));

        verify(geminiService).generateContent(anyString());
        verify(planejamentoService, never()).saveOrUpdate(any(), any());
    }

    @Test
    void testCriarPlanejamentoComIA_ExistingDiscipline_ShouldReuseExisting() {
        // Arrange
        String geminiResponse = "{\n" +
            "  \"nomePlanejamento\": \"Planejamento Teste\",\n" +
            "  \"horasDisponiveisPorSemana\": 20,\n" +
            "  \"minutosDuracaoMaximaPorSessao\": 120,\n" +
            "  \"intervalosRevisao\": \"1,3,7,14,30,60,120\",\n" +
            "  \"cicloEstudo\": {\n" +
            "    \"nome\": \"Ciclo Teste\",\n" +
            "    \"disciplinas\": [\n" +
            "      {\n" +
            "        \"nome\": \"Português\",\n" +
            "        \"peso\": 5,\n" +
            "        \"nivelConhecimento\": 3,\n" +
            "        \"ordem\": 1,\n" +
            "        \"tempoEstudoMeta\": \"06:00:00\"\n" +
            "      }\n" +
            "    ]\n" +
            "  }\n" +
            "}";

        DisciplinaDTO existingDisciplina = new DisciplinaDTO();
        existingDisciplina.setId(1L);
        existingDisciplina.setNome("Português");

        PlanejamentoDTO expectedPlanejamento = new PlanejamentoDTO();
        expectedPlanejamento.setId(1L);

        when(geminiService.generateContent(anyString())).thenReturn(geminiResponse);
        when(disciplinaService.findAllByUserId(testUser.getId()))
            .thenReturn(Arrays.asList(existingDisciplina));
        when(planejamentoService.saveOrUpdate(any(PlanejamentoDTO.class), eq(testUser)))
            .thenReturn(expectedPlanejamento);

        // Act
        PlanejamentoDTO result = planejamentoInteligentService.criarPlanejamentoComIA(testRequest, testUser);

        // Assert
        assertNotNull(result);
        verify(disciplinaService, never()).save(any(DisciplinaDTO.class), eq(testUser));
        verify(planejamentoService).saveOrUpdate(any(PlanejamentoDTO.class), eq(testUser));
    }
}
