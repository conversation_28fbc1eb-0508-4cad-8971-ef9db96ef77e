package br.com.estudoorganizado.service;

import br.com.estudoorganizado.config.GeminiConfig;
import br.com.estudoorganizado.exception.GeminiServiceException;
import okhttp3.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GeminiServiceTest {

    @Mock
    private GeminiConfig geminiConfig;

    @Mock
    private OkHttpClient httpClient;

    @Mock
    private Call call;

    @Mock
    private Response response;

    @Mock
    private ResponseBody responseBody;

    private GeminiService geminiService;

    @BeforeEach
    void setUp() {
        when(geminiConfig.getApiKey()).thenReturn("test-api-key");
        when(geminiConfig.getApiUrl()).thenReturn("https://test-api.com");
        when(geminiConfig.getTimeout()).thenReturn(30000);
        when(geminiConfig.getMaxTokens()).thenReturn(2048);
    }

    @Test
    void testConstructor_ValidConfig_ShouldCreateService() {
        // Act & Assert
        assertDoesNotThrow(() -> new GeminiService(geminiConfig));
    }

    @Test
    void testConstructor_InvalidApiKey_ShouldThrowException() {
        // Arrange
        when(geminiConfig.getApiKey()).thenReturn("your-gemini-api-key-here");

        // Act & Assert
        assertThrows(GeminiServiceException.ConfigurationException.class, 
            () -> new GeminiService(geminiConfig));
    }

    @Test
    void testConstructor_EmptyApiKey_ShouldThrowException() {
        // Arrange
        when(geminiConfig.getApiKey()).thenReturn("");

        // Act & Assert
        assertThrows(GeminiServiceException.ConfigurationException.class, 
            () -> new GeminiService(geminiConfig));
    }

    @Test
    void testGenerateContent_EmptyPrompt_ShouldThrowException() {
        // Arrange
        geminiService = new GeminiService(geminiConfig);

        // Act & Assert
        assertThrows(GeminiServiceException.InvalidResponseException.class, 
            () -> geminiService.generateContent(""));
    }

    @Test
    void testGenerateContent_SuccessfulResponse_ShouldReturnContent() throws IOException {
        // Arrange
        String prompt = "Test prompt";
        String expectedResponse = "Test response";
        String jsonResponse = "{\n" +
            "  \"candidates\": [\n" +
            "    {\n" +
            "      \"content\": {\n" +
            "        \"parts\": [\n" +
            "          {\n" +
            "            \"text\": \"" + expectedResponse + "\"\n" +
            "          }\n" +
            "        ]\n" +
            "      }\n" +
            "    }\n" +
            "  ]\n" +
            "}";

        // Mock HTTP client behavior
        try (MockedStatic<OkHttpClient> mockedClient = mockStatic(OkHttpClient.class)) {
            OkHttpClient.Builder builder = mock(OkHttpClient.Builder.class);
            when(builder.connectTimeout(anyLong(), any())).thenReturn(builder);
            when(builder.readTimeout(anyLong(), any())).thenReturn(builder);
            when(builder.writeTimeout(anyLong(), any())).thenReturn(builder);
            when(builder.build()).thenReturn(httpClient);
            
            mockedClient.when(OkHttpClient::new).thenReturn(mock(OkHttpClient.class));
            
            when(httpClient.newCall(any(Request.class))).thenReturn(call);
            when(call.execute()).thenReturn(response);
            when(response.isSuccessful()).thenReturn(true);
            when(response.body()).thenReturn(responseBody);
            when(responseBody.string()).thenReturn(jsonResponse);

            geminiService = new GeminiService(geminiConfig);

            // Act
            String result = geminiService.generateContent(prompt);

            // Assert
            assertEquals(expectedResponse, result);
        }
    }

    @Test
    void testGenerateContent_UnauthorizedResponse_ShouldThrowConfigurationException() throws IOException {
        // Arrange
        String prompt = "Test prompt";

        // Mock HTTP client behavior
        try (MockedStatic<OkHttpClient> mockedClient = mockStatic(OkHttpClient.class)) {
            OkHttpClient.Builder builder = mock(OkHttpClient.Builder.class);
            when(builder.connectTimeout(anyLong(), any())).thenReturn(builder);
            when(builder.readTimeout(anyLong(), any())).thenReturn(builder);
            when(builder.writeTimeout(anyLong(), any())).thenReturn(builder);
            when(builder.build()).thenReturn(httpClient);
            
            mockedClient.when(OkHttpClient::new).thenReturn(mock(OkHttpClient.class));
            
            when(httpClient.newCall(any(Request.class))).thenReturn(call);
            when(call.execute()).thenReturn(response);
            when(response.isSuccessful()).thenReturn(false);
            when(response.code()).thenReturn(401);
            when(response.body()).thenReturn(responseBody);
            when(responseBody.string()).thenReturn("Unauthorized");

            geminiService = new GeminiService(geminiConfig);

            // Act & Assert
            assertThrows(GeminiServiceException.ConfigurationException.class, 
                () -> geminiService.generateContent(prompt));
        }
    }

    @Test
    void testGenerateContent_RateLimitResponse_ShouldThrowCommunicationException() throws IOException {
        // Arrange
        String prompt = "Test prompt";

        // Mock HTTP client behavior
        try (MockedStatic<OkHttpClient> mockedClient = mockStatic(OkHttpClient.class)) {
            OkHttpClient.Builder builder = mock(OkHttpClient.Builder.class);
            when(builder.connectTimeout(anyLong(), any())).thenReturn(builder);
            when(builder.readTimeout(anyLong(), any())).thenReturn(builder);
            when(builder.writeTimeout(anyLong(), any())).thenReturn(builder);
            when(builder.build()).thenReturn(httpClient);
            
            mockedClient.when(OkHttpClient::new).thenReturn(mock(OkHttpClient.class));
            
            when(httpClient.newCall(any(Request.class))).thenReturn(call);
            when(call.execute()).thenReturn(response);
            when(response.isSuccessful()).thenReturn(false);
            when(response.code()).thenReturn(429);
            when(response.body()).thenReturn(responseBody);
            when(responseBody.string()).thenReturn("Rate limit exceeded");

            geminiService = new GeminiService(geminiConfig);

            // Act & Assert
            assertThrows(GeminiServiceException.CommunicationException.class, 
                () -> geminiService.generateContent(prompt));
        }
    }
}
